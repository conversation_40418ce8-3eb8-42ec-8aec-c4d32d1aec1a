{"name": "mcp-oauth-example", "version": "1.0.0", "description": "Complete OAuth 2.1 + PKCE authentication flow example for MCP", "type": "module", "engines": {"node": ">=18"}, "scripts": {"build": "tsc", "start:server": "tsx src/server/index.ts", "start:client": "tsx src/client/index.ts", "start:simple-client": "tsx src/client/simple-client.ts", "start:server:oauth": "tsx src/server/index.ts --oauth", "start:server:oauth-strict": "tsx src/server/index.ts --oauth --oauth-strict", "dev:server": "tsx --watch src/server/index.ts --oauth", "dev:client": "tsx --watch src/client/index.ts", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "express": "^5.0.1", "express-rate-limit": "^7.5.0", "cors": "^2.8.5", "pkce-challenge": "^5.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/express": "^5.0.0", "@types/cors": "^2.8.17", "@types/node": "^22.0.2", "tsx": "^4.16.5", "typescript": "^5.5.4"}, "keywords": ["mcp", "o<PERSON>h", "pkce", "authentication", "modelcontextprotocol"], "author": "MCP OAuth Example", "license": "MIT"}