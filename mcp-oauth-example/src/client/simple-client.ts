#!/usr/bin/env node

import { createServer } from 'node:http';
import { createInterface } from 'node:readline';
import { URL } from 'node:url';
import { exec } from 'node:child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { OAuthClientInformation, OAuthClientInformationFull, OAuthClientMetadata, OAuthTokens } from '@modelcontextprotocol/sdk/shared/auth.js';
import { OAuthClientProvider, UnauthorizedError } from '@modelcontextprotocol/sdk/client/auth.js';

// Configuration
const DEFAULT_SERVER_URL = 'http://localhost:3000/mcp';
const CALLBACK_PORT = 8090;
const CALLBACK_URL = `http://localhost:${CALLBACK_PORT}/callback`;

/**
 * Simple OAuth client provider for demonstration
 */
class SimpleOAuthClientProvider implements OAuthClientProvider {
  private _clientInformation?: OAuthClientInformationFull;
  private _tokens?: OAuthTokens;
  private _codeVerifier?: string;

  constructor(
    private readonly _redirectUrl: string | URL,
    private readonly _clientMetadata: OAuthClientMetadata,
    private readonly _onRedirect: (url: URL) => void
  ) {}

  get redirectUrl(): string | URL {
    return this._redirectUrl;
  }

  get clientMetadata(): OAuthClientMetadata {
    return this._clientMetadata;
  }

  async clientInformation(): Promise<OAuthClientInformation | undefined> {
    return this._clientInformation;
  }

  async saveClientInformation(clientInformation: OAuthClientInformationFull): Promise<void> {
    this._clientInformation = clientInformation;
    console.log('💾 Saved client information:', clientInformation.client_id);
  }

  async tokens(): Promise<OAuthTokens | undefined> {
    return this._tokens;
  }

  async saveTokens(tokens: OAuthTokens): Promise<void> {
    this._tokens = tokens;
    console.log('💾 Saved tokens');
  }

  async codeVerifier(): Promise<string> {
    return this._codeVerifier || '';
  }

  async saveCodeVerifier(codeVerifier: string): Promise<void> {
    this._codeVerifier = codeVerifier;
    console.log('💾 Saved code verifier');
  }

  async invalidateCredentials(type: 'tokens' | 'client' | 'all'): Promise<void> {
    switch (type) {
      case 'tokens':
        this._tokens = undefined;
        break;
      case 'client':
        this._clientInformation = undefined;
        break;
      case 'all':
        this._tokens = undefined;
        this._clientInformation = undefined;
        this._codeVerifier = undefined;
        break;
    }
    console.log(`🗑️ Invalidated ${type}`);
  }

  onRedirect(url: URL): void {
    this._onRedirect(url);
  }
}

/**
 * Simple OAuth client
 */
class SimpleOAuthClient {
  private client?: Client;
  private callbackServer?: any;

  constructor(private serverUrl: string = DEFAULT_SERVER_URL) {}

  private openBrowser(url: string): void {
    const platform = process.platform;
    let command: string;

    switch (platform) {
      case 'darwin':
        command = `open "${url}"`;
        break;
      case 'win32':
        command = `start "${url}"`;
        break;
      default:
        command = `xdg-open "${url}"`;
    }

    exec(command, (error) => {
      if (error) {
        console.error('Failed to open browser:', error);
        console.log(`Please manually open: ${url}`);
      }
    });
  }

  private async waitForOAuthCallback(): Promise<string> {
    return new Promise((resolve, reject) => {
      this.callbackServer = createServer((req, res) => {
        const url = new URL(req.url!, `http://localhost:${CALLBACK_PORT}`);
        
        if (url.pathname === '/callback') {
          const code = url.searchParams.get('code');
          const error = url.searchParams.get('error');
          
          if (error) {
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end(`<h1>Authorization Failed</h1><p>Error: ${error}</p>`);
            reject(new Error(`OAuth error: ${error}`));
            return;
          }
          
          if (code) {
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(`
              <h1>Authorization Successful!</h1>
              <p>You can now close this window and return to the terminal.</p>
              <script>setTimeout(() => window.close(), 3000);</script>
            `);
            resolve(code);
            return;
          }
        }
        
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>Not Found</h1>');
      });

      this.callbackServer.listen(CALLBACK_PORT, () => {
        console.log(`🔗 Callback server listening on port ${CALLBACK_PORT}`);
      });

      this.callbackServer.on('error', (error: any) => {
        reject(error);
      });
    });
  }

  async connect(): Promise<void> {
    console.log(`🔗 Attempting to connect to ${this.serverUrl}...`);

    const clientMetadata: OAuthClientMetadata = {
      client_name: 'Simple OAuth MCP Client',
      redirect_uris: [CALLBACK_URL],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      token_endpoint_auth_method: 'client_secret_post',
      scope: 'mcp:tools'
    };

    console.log('🔐 Creating OAuth provider...');
    const oauthProvider = new SimpleOAuthClientProvider(
      CALLBACK_URL,
      clientMetadata,
      (redirectUrl: URL) => {
        console.log(`📌 OAuth redirect - opening browser`);
        console.log(`Opening: ${redirectUrl.toString()}`);
        this.openBrowser(redirectUrl.toString());
      }
    );

    console.log('👤 Creating MCP client...');
    this.client = new Client({
      name: 'simple-oauth-client',
      version: '1.0.0',
    }, { capabilities: {} });

    console.log('🔐 Starting OAuth flow...');

    const transport = new StreamableHTTPClientTransport(
      new URL(this.serverUrl),
      oauthProvider as any
    );

    try {
      console.log('🔌 Attempting connection...');
      await this.client.connect(transport);
      console.log('✅ Connected successfully!');
      
      // Test the connection by listing tools
      await this.testConnection();
      
    } catch (error) {
      const isOAuthError = error instanceof UnauthorizedError ||
                          (error instanceof Error &&
                           (error.message.includes('HTTP 401') ||
                            error.message.includes('invalid_token') ||
                            error.message.includes('Missing Authorization header')));

      if (isOAuthError) {
        console.log('🔐 OAuth required - waiting for authorization...');
        const callbackPromise = this.waitForOAuthCallback();
        const authCode = await callbackPromise;
        await transport.finishAuth(authCode);
        console.log('🔐 Authorization completed!');
        console.log('🔌 Reconnecting...');
        await this.client.connect(transport);
        console.log('✅ Connected successfully!');
        
        // Test the connection
        await this.testConnection();
      } else {
        console.error('❌ Connection failed:', error);
        throw error;
      }
    }

    // Clean up
    if (this.callbackServer) {
      this.callbackServer.close();
    }
  }

  private async testConnection(): Promise<void> {
    console.log('\n🧪 Testing connection by listing tools...');
    
    try {
      const response = await this.client!.request({
        method: 'tools/list',
        params: {},
      } as any) as any;
      
      console.log(`✅ Found ${response.tools?.length || 0} tools:`);
      if (response.tools) {
        response.tools.forEach((tool: any, index: number) => {
          console.log(`  ${index + 1}. ${tool.name} - ${tool.description || 'No description'}`);
        });
      }
      
      console.log('\n🎉 OAuth flow completed successfully!');
      console.log('The client is now authenticated and can access MCP server resources.');
      
    } catch (error) {
      console.error('❌ Failed to test connection:', error);
    }
  }
}

// Main execution
async function main() {
  const serverUrl = process.argv[2] || DEFAULT_SERVER_URL;
  
  console.log('🚀 Starting Simple OAuth MCP Client');
  console.log(`📡 Server URL: ${serverUrl}`);
  
  const client = new SimpleOAuthClient(serverUrl);
  
  try {
    await client.connect();
  } catch (error) {
    console.error('💥 Failed to connect:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  process.exit(0);
});

// Run the client
main().catch((error) => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
