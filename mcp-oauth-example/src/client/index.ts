#!/usr/bin/env node

import { createServer } from 'node:http';
import { createInterface } from 'node:readline';
import { URL } from 'node:url';
import { exec } from 'node:child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import { OAuthClientInformation, OAuthClientInformationFull, OAuthClientMetadata, OAuthTokens } from '@modelcontextprotocol/sdk/shared/auth.js';
import {
  CallToolRequest,
  ListToolsRequest,
  CallToolResultSchema,
  ListToolsResultSchema,
  ListPromptsRequest,
  ListPromptsResultSchema,
  GetPromptRequest,
  GetPromptResultSchema,
  ListResourcesRequest,
  ListResourcesResultSchema,
  ReadResourceRequest,
  ReadResourceResultSchema
} from '@modelcontextprotocol/sdk/types.js';
import { OAuthClientProvider, UnauthorizedError } from '@modelcontextprotocol/sdk/client/auth.js';

// Configuration
const DEFAULT_SERVER_URL = 'http://localhost:3000/mcp';
const CALLBACK_PORT = 8090; // Use different port than auth server (3001)
const CALLBACK_URL = `http://localhost:${CALLBACK_PORT}/callback`;

/**
 * In-memory OAuth client provider for demonstration purposes
 * In production, you should persist tokens securely
 */
class InMemoryOAuthClientProvider implements OAuthClientProvider {
  private _clientInformation?: OAuthClientInformationFull;
  private _tokens?: OAuthTokens;
  private _codeVerifier?: string;

  constructor(
    private readonly _redirectUrl: string | URL,
    private readonly _clientMetadata: OAuthClientMetadata,
    onRedirect?: (url: URL) => void
  ) {
    this._onRedirect = onRedirect || ((url) => {
      console.log(`Redirect to: ${url.toString()}`);
    });
  }

  private _onRedirect: (url: URL) => void;

  get redirectUrl(): string | URL {
    return this._redirectUrl;
  }

  get clientMetadata(): OAuthClientMetadata {
    return this._clientMetadata;
  }

  async clientInformation(): Promise<OAuthClientInformation | undefined> {
    return this._clientInformation;
  }

  async saveClientInformation(clientInformation: OAuthClientInformationFull): Promise<void> {
    this._clientInformation = clientInformation;
    console.log('💾 Saved client information:', clientInformation.client_id);
  }

  async tokens(): Promise<OAuthTokens | undefined> {
    return this._tokens;
  }

  async saveTokens(tokens: OAuthTokens): Promise<void> {
    this._tokens = tokens;
    console.log('💾 Saved tokens');
  }

  async codeVerifier(): Promise<string | undefined> {
    return this._codeVerifier;
  }

  async saveCodeVerifier(codeVerifier: string): Promise<void> {
    this._codeVerifier = codeVerifier;
    console.log('💾 Saved code verifier');
  }

  async invalidateCredentials(type: 'tokens' | 'client' | 'all'): Promise<void> {
    switch (type) {
      case 'tokens':
        this._tokens = undefined;
        console.log('🗑️ Invalidated tokens');
        break;
      case 'client':
        this._clientInformation = undefined;
        console.log('🗑️ Invalidated client information');
        break;
      case 'all':
        this._tokens = undefined;
        this._clientInformation = undefined;
        this._codeVerifier = undefined;
        console.log('🗑️ Invalidated all credentials');
        break;
    }
  }

  onRedirect(url: URL): void {
    this._onRedirect(url);
  }
}

/**
 * Simple OAuth client that demonstrates the complete OAuth flow
 */
class SimpleOAuthClient {
  private client?: Client;
  private callbackServer?: any;
  private serverUrl: string;

  constructor(serverUrl: string = DEFAULT_SERVER_URL) {
    this.serverUrl = serverUrl;
  }

  /**
   * Opens a URL in the default browser
   */
  private openBrowser(url: string): void {
    const platform = process.platform;
    let command: string;

    switch (platform) {
      case 'darwin':
        command = `open "${url}"`;
        break;
      case 'win32':
        command = `start "${url}"`;
        break;
      default:
        command = `xdg-open "${url}"`;
    }

    exec(command, (error) => {
      if (error) {
        console.error('Failed to open browser:', error);
        console.log(`Please manually open: ${url}`);
      }
    });
  }

  /**
   * Sets up a temporary HTTP server to receive the OAuth callback
   */
  private async waitForOAuthCallback(): Promise<string> {
    return new Promise((resolve, reject) => {
      this.callbackServer = createServer((req, res) => {
        const url = new URL(req.url!, `http://localhost:${CALLBACK_PORT}`);
        
        if (url.pathname === '/callback') {
          const code = url.searchParams.get('code');
          const error = url.searchParams.get('error');
          
          if (error) {
            res.writeHead(400, { 'Content-Type': 'text/html' });
            res.end(`<h1>Authorization Failed</h1><p>Error: ${error}</p>`);
            reject(new Error(`OAuth error: ${error}`));
            return;
          }
          
          if (code) {
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(`
              <h1>Authorization Successful!</h1>
              <p>You can now close this window and return to the terminal.</p>
              <script>setTimeout(() => window.close(), 3000);</script>
            `);
            resolve(code);
            return;
          }
        }
        
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>Not Found</h1>');
      });

      this.callbackServer.listen(CALLBACK_PORT, () => {
        console.log(`🔗 Callback server listening on port ${CALLBACK_PORT}`);
      });

      this.callbackServer.on('error', (error: any) => {
        reject(error);
      });
    });
  }

  /**
   * Attempts to establish a connection with the given OAuth provider
   */
  private async attemptConnection(oauthProvider: InMemoryOAuthClientProvider): Promise<void> {
    console.log('🚢 Creating transport...');
    const transport = new StreamableHTTPClientTransport(
      new URL(this.serverUrl),
      oauthProvider
    );
    console.log('🚢 Transport created');

    try {
      console.log('🔌 Attempting connection (this will trigger OAuth redirect)...');
      await this.client!.connect(transport);
      console.log('✅ Connected successfully');
    } catch (error) {
      // Check for OAuth-related errors (401 Unauthorized or UnauthorizedError)
      const isOAuthError = error instanceof UnauthorizedError ||
                          (error instanceof Error &&
                           (error.message.includes('HTTP 401') ||
                            error.message.includes('invalid_token') ||
                            error.message.includes('Missing Authorization header')));

      if (isOAuthError) {
        console.log('🔐 OAuth required - waiting for authorization...');
        const callbackPromise = this.waitForOAuthCallback();
        const authCode = await callbackPromise;
        await transport.finishAuth(authCode);
        console.log('🔐 Authorization code received:', authCode);
        console.log('🔌 Reconnecting with authenticated transport...');
        await this.attemptConnection(oauthProvider);
      } else {
        console.error('❌ Connection failed with non-auth error:', error);
        throw error;
      }
    }
  }

  /**
   * Establishes connection to the MCP server with OAuth authentication
   */
  async connect(): Promise<void> {
    console.log(`🔗 Attempting to connect to ${this.serverUrl}...`);

    const clientMetadata: OAuthClientMetadata = {
      client_name: 'Simple OAuth MCP Client',
      redirect_uris: [CALLBACK_URL],
      grant_types: ['authorization_code', 'refresh_token'],
      response_types: ['code'],
      token_endpoint_auth_method: 'client_secret_post',
      scope: 'mcp:tools'
    };

    console.log('🔐 Creating OAuth provider...');
    const oauthProvider = new InMemoryOAuthClientProvider(
      CALLBACK_URL,
      clientMetadata,
      (redirectUrl: URL) => {
        console.log(`📌 OAuth redirect handler called - opening browser`);
        console.log(`Opening browser to: ${redirectUrl.toString()}`);
        this.openBrowser(redirectUrl.toString());
      }
    );
    console.log('🔐 OAuth provider created');

    console.log('👤 Creating MCP client...');
    this.client = new Client({
      name: 'simple-oauth-client',
      version: '1.0.0',
    }, { capabilities: {} });
    console.log('👤 Client created');

    console.log('🔐 Starting OAuth flow...');

    await this.attemptConnection(oauthProvider);

    // Start interactive loop
    await this.interactiveLoop();
  }

  /**
   * Interactive loop for testing MCP functionality
   */
  private async interactiveLoop(): Promise<void> {
    const rl = createInterface({
      input: process.stdin,
      output: process.stdout,
    });

    const question = (prompt: string): Promise<string> => {
      return new Promise((resolve) => {
        rl.question(prompt, resolve);
      });
    };

    console.log('\n🎉 Connected! You can now interact with the MCP server.');
    console.log('Available commands:');
    console.log('  1 - List available tools');
    console.log('  2 - Call a tool');
    console.log('  3 - List available prompts');
    console.log('  4 - Get a prompt');
    console.log('  5 - List available resources');
    console.log('  6 - Read a resource');
    console.log('  q - Quit');

    while (true) {
      try {
        const choice = await question('\nEnter your choice: ');

        switch (choice.trim()) {
          case '1':
            await this.listTools();
            break;
          case '2':
            await this.callTool(question);
            break;
          case '3':
            await this.listPrompts();
            break;
          case '4':
            await this.getPrompt(question);
            break;
          case '5':
            await this.listResources();
            break;
          case '6':
            await this.readResource(question);
            break;
          case 'q':
          case 'quit':
            console.log('👋 Goodbye!');
            rl.close();
            if (this.callbackServer) {
              this.callbackServer.close();
            }
            process.exit(0);
            break;
          default:
            console.log('❌ Invalid choice. Please try again.');
        }
      } catch (error) {
        console.error('❌ Error:', error);
      }
    }
  }

  /**
   * Lists available tools
   */
  private async listTools(): Promise<void> {
    console.log('\n📋 Listing available tools...');

    const request: ListToolsRequest = {
      method: 'tools/list',
      params: {},
    };

    const response = await this.client!.request(request, ListToolsResultSchema);

    if (response.tools.length === 0) {
      console.log('No tools available.');
      return;
    }

    console.log(`Found ${response.tools.length} tool(s):`);
    response.tools.forEach((tool, index) => {
      console.log(`  ${index + 1}. ${tool.name}`);
      if (tool.description) {
        console.log(`     Description: ${tool.description}`);
      }
    });
  }

  /**
   * Calls a specific tool
   */
  private async callTool(question: (prompt: string) => Promise<string>): Promise<void> {
    console.log('\n🔧 Calling a tool...');

    // First list available tools
    const listRequest: ListToolsRequest = {
      method: 'tools/list',
      params: {},
    };

    const listResponse = await this.client!.request(listRequest, ListToolsResultSchema);

    if (listResponse.tools.length === 0) {
      console.log('No tools available.');
      return;
    }

    console.log('Available tools:');
    listResponse.tools.forEach((tool, index) => {
      console.log(`  ${index + 1}. ${tool.name} - ${tool.description || 'No description'}`);
    });

    const toolChoice = await question('Enter tool number: ');
    const toolIndex = parseInt(toolChoice) - 1;

    if (toolIndex < 0 || toolIndex >= listResponse.tools.length) {
      console.log('❌ Invalid tool number.');
      return;
    }

    const selectedTool = listResponse.tools[toolIndex];
    console.log(`Selected tool: ${selectedTool.name}`);

    // Get arguments for the tool
    let args: any = {};

    if (selectedTool.inputSchema && selectedTool.inputSchema.properties) {
      console.log('\nTool requires arguments:');
      const properties = selectedTool.inputSchema.properties;

      for (const [propName, propSchema] of Object.entries(properties)) {
        const schema = propSchema as any;
        const isRequired = selectedTool.inputSchema.required?.includes(propName) || false;
        const prompt = `${propName}${isRequired ? ' (required)' : ' (optional)'}: `;

        if (schema.description) {
          console.log(`  ${schema.description}`);
        }

        const value = await question(prompt);

        if (value.trim() || isRequired) {
          // Try to parse based on schema type
          if (schema.type === 'number' || schema.type === 'integer') {
            args[propName] = parseFloat(value);
          } else if (schema.type === 'boolean') {
            args[propName] = value.toLowerCase() === 'true' || value.toLowerCase() === 'yes';
          } else if (schema.type === 'array') {
            args[propName] = value.split(',').map(s => s.trim());
          } else {
            args[propName] = value;
          }
        }
      }
    }

    // Call the tool
    const callRequest: CallToolRequest = {
      method: 'tools/call',
      params: {
        name: selectedTool.name,
        arguments: args,
      },
    };

    console.log(`\n🔧 Calling ${selectedTool.name} with arguments:`, JSON.stringify(args, null, 2));

    const callResponse = await this.client!.request(callRequest, CallToolResultSchema);

    console.log('\n📄 Tool response:');
    if (callResponse.content) {
      callResponse.content.forEach((content) => {
        if (content.type === 'text') {
          console.log(content.text);
        } else {
          console.log('Non-text content:', content);
        }
      });
    }

    if (callResponse.isError) {
      console.log('⚠️ Tool returned an error');
    }
  }

  /**
   * Lists available prompts
   */
  private async listPrompts(): Promise<void> {
    console.log('\n📝 Listing available prompts...');

    try {
      const response = await this.client!.request({
        method: 'prompts/list',
        params: {},
      } as any) as any;

      if (!response.prompts || response.prompts.length === 0) {
        console.log('No prompts available.');
        return;
      }

      console.log(`Found ${response.prompts.length} prompt(s):`);
      response.prompts.forEach((prompt: any, index: number) => {
        console.log(`  ${index + 1}. ${prompt.name}`);
        if (prompt.description) {
          console.log(`     Description: ${prompt.description}`);
        }
      });
    } catch (error) {
      console.error('Error listing prompts:', error);
    }
  }

  /**
   * Gets a specific prompt
   */
  private async getPrompt(question: (prompt: string) => Promise<string>): Promise<void> {
    console.log('\n📝 Getting a prompt...');

    // First list available prompts
    const listResponse = await this.client!.request({
      method: 'prompts/list',
      params: {},
    });

    if (!listResponse.prompts || listResponse.prompts.length === 0) {
      console.log('No prompts available.');
      return;
    }

    console.log('Available prompts:');
    listResponse.prompts.forEach((prompt: any, index: number) => {
      console.log(`  ${index + 1}. ${prompt.name} - ${prompt.description || 'No description'}`);
    });

    const promptChoice = await question('Enter prompt number: ');
    const promptIndex = parseInt(promptChoice) - 1;

    if (promptIndex < 0 || promptIndex >= listResponse.prompts.length) {
      console.log('❌ Invalid prompt number.');
      return;
    }

    const selectedPrompt = listResponse.prompts[promptIndex];
    console.log(`Selected prompt: ${selectedPrompt.name}`);

    // Get arguments for the prompt if needed
    let args: any = {};

    if (selectedPrompt.arguments && selectedPrompt.arguments.length > 0) {
      console.log('\nPrompt requires arguments:');

      for (const arg of selectedPrompt.arguments) {
        const prompt = `${arg.name}${arg.required ? ' (required)' : ' (optional)'}: `;

        if (arg.description) {
          console.log(`  ${arg.description}`);
        }

        const value = await question(prompt);

        if (value.trim() || arg.required) {
          args[arg.name] = value;
        }
      }
    }

    // Get the prompt
    const getResponse = await this.client!.request({
      method: 'prompts/get',
      params: {
        name: selectedPrompt.name,
        arguments: args,
      },
    });

    console.log('\n📄 Prompt response:');
    if (getResponse.description) {
      console.log(`Description: ${getResponse.description}`);
    }

    if (getResponse.messages) {
      getResponse.messages.forEach((message: any, index: number) => {
        console.log(`\nMessage ${index + 1} (${message.role}):`);
        if (message.content.type === 'text') {
          console.log(message.content.text);
        } else {
          console.log('Non-text content:', message.content);
        }
      });
    }
  }

  /**
   * Lists available resources
   */
  private async listResources(): Promise<void> {
    console.log('\n📚 Listing available resources...');

    const response = await this.client!.request({
      method: 'resources/list',
      params: {},
    });

    if (!response.resources || response.resources.length === 0) {
      console.log('No resources available.');
      return;
    }

    console.log(`Found ${response.resources.length} resource(s):`);
    response.resources.forEach((resource: any, index: number) => {
      console.log(`  ${index + 1}. ${resource.uri}`);
      if (resource.name) {
        console.log(`     Name: ${resource.name}`);
      }
      if (resource.description) {
        console.log(`     Description: ${resource.description}`);
      }
      if (resource.mimeType) {
        console.log(`     MIME Type: ${resource.mimeType}`);
      }
    });
  }

  /**
   * Reads a specific resource
   */
  private async readResource(question: (prompt: string) => Promise<string>): Promise<void> {
    console.log('\n📖 Reading a resource...');

    // First list available resources
    const listResponse = await this.client!.request({
      method: 'resources/list',
      params: {},
    });

    if (!listResponse.resources || listResponse.resources.length === 0) {
      console.log('No resources available.');
      return;
    }

    console.log('Available resources:');
    listResponse.resources.forEach((resource: any, index: number) => {
      console.log(`  ${index + 1}. ${resource.uri} - ${resource.name || 'No name'}`);
    });

    const resourceChoice = await question('Enter resource number: ');
    const resourceIndex = parseInt(resourceChoice) - 1;

    if (resourceIndex < 0 || resourceIndex >= listResponse.resources.length) {
      console.log('❌ Invalid resource number.');
      return;
    }

    const selectedResource = listResponse.resources[resourceIndex];
    console.log(`Selected resource: ${selectedResource.uri}`);

    // Read the resource
    const readResponse = await this.client!.request({
      method: 'resources/read',
      params: {
        uri: selectedResource.uri,
      },
    });

    console.log('\n📄 Resource content:');
    if (readResponse.contents) {
      readResponse.contents.forEach((content: any) => {
        if (content.type === 'text') {
          console.log(content.text);
        } else {
          console.log('Non-text content:', content);
        }
      });
    }
  }
}

// Main execution
async function main() {
  const serverUrl = process.argv[2] || DEFAULT_SERVER_URL;

  console.log('🚀 Starting Simple OAuth MCP Client');
  console.log(`📡 Server URL: ${serverUrl}`);

  const client = new SimpleOAuthClient(serverUrl);

  try {
    await client.connect();
  } catch (error) {
    console.error('💥 Failed to connect:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Shutting down...');
  process.exit(0);
});

// Run the client
main().catch((error) => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
