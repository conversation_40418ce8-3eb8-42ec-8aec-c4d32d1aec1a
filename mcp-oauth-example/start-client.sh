#!/bin/bash

# MCP OAuth Client Startup Script

echo "🚀 Starting MCP OAuth Client..."
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Parse command line arguments
SERVER_URL="http://localhost:3000/mcp"

for arg in "$@"; do
    case $arg in
        --server=*)
            SERVER_URL="${arg#*=}"
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --server=URL    MCP server URL (default: http://localhost:3000/mcp)"
            echo "  --help, -h      Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                                    # Connect to default server"
            echo "  $0 --server=http://localhost:3000/mcp # Connect to custom server"
            exit 0
            ;;
    esac
done

echo "📊 Configuration:"
echo "  Server URL: $SERVER_URL"
echo "  Callback Port: 8090"
echo ""

echo "🔧 Running: npm run start:simple-client"
echo ""

# Check if server is running
echo "🔍 Checking if MCP server is running..."
if curl -s --connect-timeout 3 "http://localhost:3000" > /dev/null 2>&1; then
    echo "✅ MCP server is running"
else
    echo "⚠️  MCP server doesn't seem to be running on port 3000"
    echo "   Make sure to start the server first with: ./start-server.sh --oauth"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo ""
echo "🎯 Starting OAuth client..."
echo "   The client will automatically open your browser for OAuth authorization"
echo "   Follow the instructions in the browser to complete the OAuth flow"
echo ""

# Start the client
exec npm run start:simple-client
