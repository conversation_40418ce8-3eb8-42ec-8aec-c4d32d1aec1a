# Troubleshooting Guide

## OAuth Authentication Issues

### Error: `{"error":"invalid_client","error_description":"Invalid client_id"}`

**Cause**: The client is not registered with the OAuth server.

**Solutions**:

1. **Use the pre-registered test client** (easiest for testing):
   - Client ID: `test-client-id`
   - Client Secret: `test-client-secret`
   - Redirect URI: `http://localhost:8080/callback` or `https://cursor.sh/oauth/callback`

2. **Register a new client dynamically**:
   ```bash
   npm run oauth:register
   ```

3. **Manual client registration**:
   ```bash
   curl -X POST http://localhost:3001/register \
     -H "Content-Type: application/json" \
     -d '{
       "redirect_uris": ["https://your-app.com/callback"],
       "client_name": "Your Client Name",
       "scope": "mcp:tools"
     }'
   ```

### Error: `{"error":"invalid_grant","error_description":"Invalid authorization code"}`

**Cause**: The authorization code is expired, invalid, or already used.

**Solutions**:
1. Obtain a new authorization code by repeating the authorization flow
2. Ensure the authorization code is used immediately after receiving it
3. Check that the redirect URI matches exactly what was registered

### Error: `{"error":"invalid_request","error_description":"Missing code_verifier"}`

**Cause**: PKCE code verifier is missing or incorrect.

**Solutions**:
1. Ensure you're using the same `code_verifier` that was used to generate the `code_challenge`
2. Use the OAuth examples provided: `npm run oauth:register` and `npm run oauth:exchange`

## Connection Issues

### Error: `ECONNREFUSED` when connecting to MCP server

**Cause**: The MCP server is not running or running on a different port.

**Solutions**:
1. Start the server: `npm run dev` or `npm run dev:oauth`
2. Check the port configuration (default: 3000 for MCP, 3001 for OAuth)
3. Verify the server URL in your client configuration

### Error: `Invalid or missing session ID`

**Cause**: The client is not properly managing the MCP session.

**Solutions**:
1. Ensure the client sends an `initialize` request first
2. Include the `Mcp-Session-Id` header in subsequent requests
3. Use the provided test client: `npm run test:client`

## Cursor IDE Integration Issues

### Cursor can't connect to MCP server with OAuth

**Checklist**:
1. ✅ Server is running with OAuth enabled: `npm run dev:oauth`
2. ✅ OAuth server is accessible at `http://localhost:3001`
3. ✅ Use pre-registered test client credentials:
   - Client ID: `test-client-id`
   - Client Secret: `test-client-secret`
   - Redirect URI: `https://cursor.sh/oauth/callback`
4. ✅ Scope is set to: `mcp:tools`
5. ✅ Authorization server URL: `http://localhost:3001`

### Cursor OAuth flow fails

**Common issues**:
1. **Wrong redirect URI**: Ensure it matches `https://cursor.sh/oauth/callback`
2. **Network access**: Cursor needs to reach `http://localhost:3001`
3. **Firewall**: Check if local firewall is blocking the connection

## Development Issues

### TypeScript compilation errors

**Solutions**:
1. Install dependencies: `npm install`
2. Check TypeScript version: `npx tsc --version`
3. Clean build: `rm -rf dist && npm run build`

### Missing dependencies

**Solutions**:
1. Run the install script: `./install.sh`
2. Manual installation: `npm install`
3. Check Node.js version: `node --version` (requires 18+)

## Testing and Debugging

### Enable debug logging

Add environment variable:
```bash
DEBUG=mcp:* npm run dev:oauth
```

### Test OAuth flow manually

1. Register client: `npm run oauth:register`
2. Follow the authorization URL in browser
3. Extract authorization code from redirect
4. Exchange for token: `npm run oauth:exchange CLIENT_ID CLIENT_SECRET AUTH_CODE CODE_VERIFIER`

### Test MCP without OAuth

```bash
npm run dev
npm run test:client
```

## Common Configuration Issues

### Port conflicts

**Error**: `EADDRINUSE: address already in use`

**Solutions**:
1. Change ports using environment variables:
   ```bash
   MCP_PORT=3002 MCP_AUTH_PORT=3003 npm run dev:oauth
   ```
2. Kill existing processes: `lsof -ti:3000 | xargs kill -9`

### CORS issues

**Error**: Cross-origin request blocked

**Solutions**:
1. The server allows all origins by default
2. Check if you're using HTTPS with HTTP server (use HTTPS for both)
3. Verify the request includes proper headers

## Getting Help

If you're still experiencing issues:

1. Check the server logs for detailed error messages
2. Verify your client implementation against the provided examples
3. Test with the included test client first: `npm run test:client`
4. For OAuth issues, test the flow manually using the provided scripts

## Production Considerations

⚠️ **This is a demo implementation**. For production use:

1. Use persistent storage for OAuth clients and tokens
2. Implement proper rate limiting
3. Use HTTPS for all OAuth endpoints
4. Implement proper client credential storage
5. Add comprehensive logging and monitoring
